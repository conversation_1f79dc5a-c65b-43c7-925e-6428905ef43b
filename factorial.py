import math

def factorial_iterative(n: int) -> int:
    if not isinstance(n, int) or n < 0:
        raise ValueError("Factorial is only defined for non-negative integers.")
    if n == 0:
        return 1
    result = 1
    for i in range(1, n + 1):
        result *= i
    return result

def factorial_recursive(n: int) -> int:
    if not isinstance(n, int) or n < 0:
        raise ValueError("Factorial is only defined for non-negative integers.")
    if n == 0:
        return 1
    else:
        return n * factorial_recursive(n - 1)

def factorial_pythonic(n: int) -> int:
    return math.factorial(n)

# --- Example Usage ---
if __name__ == "__main__":
    try:
        number_to_test = int(input("Enter a non-negative integer to calculate its factorial: "))
        print(f"Calculating factorial for {number_to_test}...")

        # Method 1: Iterative
        print(f"  Iterative result: {factorial_iterative(number_to_test)}")

        # Method 2: Recursive
        print(f"  Recursive result: {factorial_recursive(number_to_test)}")

        # Method 3: Pythonic (math.factorial)
        print(f"  Pythonic result:  {factorial_pythonic(number_to_test)}")
    except ValueError as e:
        print(f"Error: {e}")
