# Simple Factorial Program

def factorial(n):
    """Calculate factorial of a number"""
    if n == 0 or n == 1:
        return 1

    result = 1
    for i in range(2, n + 1):
        result = result * i
    return result

# Show how factorial works step by step
def show_factorial_steps(n):
    print(f"Calculating {n}! step by step:")
    if n == 0 or n == 1:
        print(f"{n}! = 1")
        return

    result = 1
    steps = []
    for i in range(1, n + 1):
        result = result * i
        steps.append(str(i))

    print(f"{n}! = {' × '.join(steps)} = {result}")

# Main program
print("=== Factorial Calculator ===")
print()

# Show what factorial means
print("What is factorial?")
print("n! = 1 × 2 × 3 × ... × n")
print("Examples:")
print("3! = 1 × 2 × 3 = 6")
print("5! = 1 × 2 × 3 × 4 × 5 = 120")
print()

# Calculate some factorials
print("Factorial calculations:")
for num in range(0, 8):
    result = factorial(num)
    print(f"{num}! = {result}")
print()

# Show detailed steps for some numbers
print("Step-by-step calculations:")
test_numbers = [3, 5, 6]
for num in test_numbers:
    show_factorial_steps(num)
    print()

# Let user input a number
print("Try your own number:")
try:
    user_num = int(input("Enter a number: "))
    if user_num < 0:
        print("Factorial is not defined for negative numbers!")
    else:
        result = factorial(user_num)
        print(f"{user_num}! = {result}")
        show_factorial_steps(user_num)
except:
    print("Please enter a valid number!")